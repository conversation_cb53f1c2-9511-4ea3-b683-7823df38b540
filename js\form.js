const scriptURL = 'https://script.google.com/macros/s/AKfycbw63N5MMrN2s-Xzlse0gD0w3kyUElzepOS4jCBxfNzzI9cafEGClnnsYi53y6gro8sx/exec'
const form = document.forms['submit-to-google-sheet']
form.addEventListener('submit', e => {
  e.preventDefault()
  fetch(scriptURL, { method: 'POST', body: new FormData(form) })
    .then(response => console.log('Success!', response))
    .catch(error => console.error('Error!', error.message))
  if (response => console.log('Success!', response)) {
    swal({
      title: "Thank You!",
      text: "Your message sent successfully. We will get back to you in 15 minutes.",
      icon: "success",
      successMode: true,
    })
      .then((willDelete) => {
        if (willDelete) {
          window.location = window.location.href;
        } else {
          window.location = window.location.href;
        }
      });
  }
  else {
    swal({
      title: "Sorry!",
      text: "Your message not sent. Please try after some time.",
      icon: "warning",
      dangerMode: true,
    })
      .then((willDelete) => {
        if (willDelete) {
          window.location = window.location.href;
        } else {
          window.location = window.location.href;
        }
      });
  }
})
